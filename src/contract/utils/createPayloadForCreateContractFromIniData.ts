import * as DAT5 from 'api2dat5'
import { format, parse } from 'date-fns'
import { findSectionIgnoreCase } from './findSectionIgnoreCase'
import { findKeyValueIgnoreCase } from './findKeyValueIgnoreCase'

function formatFloat(
  value?: string,
  options?: { thousandSeparator?: '.' | ',' | ' '; floatDecimalSeparator?: '.' | ','; floatFixedDecimalScale?: number }
): string {
  if (!value || typeof value !== 'string') return ''
  value = value.replace(/,/g, '.')
  const floatNumber = parseFloat(value)
  if (Number.isNaN(floatNumber)) return ''

  const { thousandSeparator, floatDecimalSeparator, floatFixedDecimalScale } = options || {}
  let result = floatNumber.toString()
  if (floatFixedDecimalScale) result = floatNumber.toFixed(floatFixedDecimalScale)
  if (floatDecimalSeparator) result = result.replace('.', floatDecimalSeparator)
  if (thousandSeparator) result = result.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)

  return result
}

function formatInteger(value?: string, options?: { thousandSeparator?: '.' | ',' | ' ' }): string {
  if (!value || typeof value !== 'string') return ''
  const integer = parseInt(value)
  if (Number.isNaN(integer)) return ''

  const { thousandSeparator } = options || {}
  if (thousandSeparator) return integer.toString().replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)
  return integer.toString()
}

export function parseValue(value: string | string[], config: DAT.IniMappingSubfield) {
  const {
    type,
    floatFixedDecimalScale,
    floatDecimalSeparator,
    thousandSeparator,
    initialDateFormat,
    dateFormat,
    index
  } = config
  let result = Array.isArray(value) ? value[index || 0] : value

  switch (type) {
    case 'date':
      let parsedDate = new Date(result)
      if (initialDateFormat) parsedDate = parse(result, initialDateFormat, new Date())
      const isInvalidDate = isNaN(parsedDate.getTime())
      if (isInvalidDate) result = ''
      if (isInvalidDate) break
      if (dateFormat) result = format(parsedDate, dateFormat)
      break
    case 'integer':
      result = formatInteger(result, { thousandSeparator })
      break
    case 'float':
      result = formatFloat(result, { thousandSeparator, floatDecimalSeparator, floatFixedDecimalScale })
  }

  return result
}

function addValueToPayload(
  value: string,
  config: DAT.IniMappingSubfield,
  payloadForCreateOrUpdateContract: DAT5.MyClaimExternalService_schema1.createOrUpdateContract
) {
  const { dossierData, templateData, memoFieldType = 'xs:string' } = config

  if (templateData) {
    if (!payloadForCreateOrUpdateContract.templateData.entry) payloadForCreateOrUpdateContract.templateData.entry = []
    payloadForCreateOrUpdateContract.templateData.entry.push({
      key: templateData,
      value: { _value: value, _attr_type: memoFieldType }
    })
  }

  // dossierData = Ex. 'Vehicle.RegistrationData.NumberPlate' or 'Vehicle.DatECodeEquipment.[0].EquipmentId'
  if (dossierData) {
    const pathParts = dossierData.split('.')
    pathParts.reduce((acc, key, index) => {
      const isLastKey = index === pathParts.length - 1
      const arrayIndex = Number(key.match(/^\[(\d+)]$/)?.[1])

      if (!Number.isNaN(arrayIndex)) {
        if (isLastKey) {
          if (Array.isArray(acc)) {
            acc.push(value)
          } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            acc = [value]
          }
        } else {
          if (Array.isArray(acc)) {
            acc.push({})
          } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            acc = [{}]
          }
        }
      } else {
        if (isLastKey) {
          acc[key] = value
        } else {
          if (!acc[key]) {
            acc[key] = {}
          }
        }
      }

      return acc
    }, payloadForCreateOrUpdateContract.Dossier)
  }

  return payloadForCreateOrUpdateContract
}

export function createPayloadForCreateContractFromIniData(
  iniData: Record<string, Array<Record<string, string | string[]>>>,
  iniConfig: DAT.IniMappingContent | null
) {
  if (!iniConfig?.fields) return null
  const { fields, contractBaseData } = iniConfig
  const payloadForCreateOrUpdateContract: DAT5.MyClaimExternalService_schema1.createOrUpdateContract = {
    Dossier: {
      Country: contractBaseData?.country || '',
      Language: contractBaseData?.language || ''
    },
    templateData: {
      entry: []
    },
    networkType: contractBaseData?.networkType,
    contractType: contractBaseData?.contractType,
    templateId: contractBaseData?.templateId
  }

  for (const field of fields) {
    const { iniSection, iniKey, separator, subfields } = field
    if (!iniSection || !iniKey || !subfields) continue

    const section = findSectionIgnoreCase(iniData, iniSection)
    if (!section) continue

    const value = findKeyValueIgnoreCase(section, iniKey)
    if (!value) continue

    if (Array.isArray(value)) {
      subfields.forEach(subfield => {
        const parsedValue = parseValue(value, subfield)
        addValueToPayload(parsedValue, subfield, payloadForCreateOrUpdateContract)
      })
    } else {
      if (separator) {
        const splitValue = value.split(separator)
        subfields.forEach(subfield => {
          const parsedValue = parseValue(splitValue, subfield)
          addValueToPayload(parsedValue, subfield, payloadForCreateOrUpdateContract)
        })
      } else {
        const subfield = subfields[0] || {}
        const parsedValue = parseValue(value, subfield)
        addValueToPayload(parsedValue, subfield, payloadForCreateOrUpdateContract)
      }
    }
  }

  return payloadForCreateOrUpdateContract
}
