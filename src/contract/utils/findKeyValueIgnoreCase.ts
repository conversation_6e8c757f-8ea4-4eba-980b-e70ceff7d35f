export function findKeyValueIgnoreCase(
  section: Array<Record<string, string | string[]>>,
  keyName?: string,
  sectionIndex?: number
): string[] | string | null {
  const keyNameLower = keyName?.toLowerCase()
  if (!keyNameLower) return null

  for (const [key, value] of Object.entries(section[sectionIndex || 0])) {
    if (key.toLowerCase() === keyNameLower) {
      return value
    }
  }

  return null
}
