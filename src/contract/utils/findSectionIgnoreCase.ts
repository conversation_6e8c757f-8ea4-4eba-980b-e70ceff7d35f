export function findSectionIgnoreCase(
  parsedIni: Record<string, Array<Record<string, string | string[]>>>,
  sectionName?: string
): Array<Record<string, string | string[]>> | null {
  const sectionNameLower = sectionName?.toLowerCase()
  if (!sectionNameLower) return null

  for (const [key, value] of Object.entries(parsedIni)) {
    if (key.toLowerCase() === sectionNameLower) {
      return value
    }
  }

  return null
}
