import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common'
import * as DAT5 from 'api2dat5'
import { isAxiosError } from 'axios'

import { MyClaimService } from 'src/my-claim/my-claim.service'
import { getParsedArraySafe } from 'src/shared/soap/helpers/getParsedArraySafe'
import { getFieldText } from 'src/shared/soap/helpers/getFieldText'
import { MOCK_MAPPING_CONFIG } from './constants'
import { parseIniFile, detectIniConfig } from './utils'
import {
  CreateClaimWithAnotherAccountResponse,
  PayloadForCreateClaimWithAnotherAccount,
  PayloadForExtractEntry,
  PayloadForPhotoUpload,
  PayloadForUpdateContract,
  ImportIniResult
} from './types'
import { SoapFieldPrimitives } from 'src/shared/soap/types'
import { UserService } from 'src/user/user.service'
import { microCalcCredentials } from './constants'
import { DatService } from 'src/shared/dat/dat.service'
import { MyClaimProxyService } from 'src/myClaim-proxy/myClaim-proxy.service'
import { HttpHeaders, ProxyRequest } from 'src/myClaim-proxy/types'
import { LoginResult } from 'src/user/interfaces/login-result.interface'

@Injectable()
export class ContractService {
  constructor(
    private datService: DatService,
    private myClaimService: MyClaimService,
    private userService: UserService,
    private myClaimProxyService: MyClaimProxyService
  ) {}

  uploadPhoto({ contractId, attachmentItem }: PayloadForPhotoUpload) {
    return this.myClaimService.uploadAttachmentByFolderID({
      contractId,
      attachmentItem
    })
  }

  updateContract(data: PayloadForUpdateContract): Promise<number> {
    let { contractId, contractType, networkType } = data

    if ('contract' in data) {
      contractId = data.contract.Dossier?.DossierId || contractId
      contractType = data.contract.complexTemplateData?.templateType || contractType
      networkType = data.contract.networkType || networkType
    }

    if (!contractId || !contractType || !networkType)
      throw new InternalServerErrorException(
        `${this.updateContract.name}: one of these is undefined: contractId: ${contractId}, contractType: ${contractType}, networkType: ${networkType}`
      )

    return this.myClaimService.createOrUpdateContract({
      ...data,
      Dossier: {
        ...data.Dossier,
        Vehicle: {
          ...data.Dossier?.Vehicle,
          VehicleDataItaly: data.Dossier?.Vehicle?.VehicleDataItaly || {}
        }
      },
      contractId,
      contractType,
      networkType
    })
  }

  createContract(data: PayloadForUpdateContract): Promise<number> {
    const now = new Date()
    const dateString = now
      .toISOString()
      .replace(/[-T:.Z]/g, '')
      .slice(0, 14)
    return this.myClaimService.createOrUpdateContract({
      templateId: 151527,
      contractType: 'vro_domus_calculation',
      networkType: 'ANIA',
      ...data,
      Dossier: {
        Vehicle: {
          VehicleDataItaly: data.Dossier?.Vehicle?.VehicleDataItaly || {}
        },
        Name: `CLAIM_${dateString}`,
        Country: 'IT',
        Currency: 'EUR',
        ...data.Dossier
      }
    })
  }

  extractEntry<TValue extends SoapFieldPrimitives>({ contract, key }: PayloadForExtractEntry): TValue | undefined {
    const entries = getParsedArraySafe(contract.customTemplateData?.entry)
    const targetEntry = entries.find(entry => entry.key.toLowerCase() === key)

    return getFieldText(targetEntry?.value) as TValue | undefined
  }

  async createOrUpdateContractWithProxy(body: ProxyRequest, token: string) {
    try {
      const headers: HttpHeaders = {
        ...(body.headers || {})
      }
      await this.myClaimProxyService.setAuthHeaders(body, token, headers, body.network)

      const options: DAT5.Options = {
        credentials: {
          'dat-authorizationtoken': token
        },
        headers
      }

      const result = await DAT5.MyClaimExternalService.createOrUpdateContractN(body.data, options)

      return result.responseObj.return
    } catch (err) {
      if (!isAxiosError(err)) {
        return Logger.error(err, 'CLAIM_UPSERT')
      }
      Logger.error(`Axios error message: ${err.message}`, 'CLAIM_UPSERT')

      if (err.config) {
        Logger.error(`Axios request: ${err.config.method?.toUpperCase()} ${err.config.url}`, 'CLAIM_UPSERT')
        if (err.config.data) {
          Logger.error(`Axios request body: ${JSON.stringify(err.config.data)}`, 'CLAIM_UPSERT')
        }
      }

      if (err.response) {
        Logger.error(`Axios response status: ${err.response.status} ${err.response.statusText}`, 'CLAIM_UPSERT')
        Logger.error(`Axios response data: ${JSON.stringify(err.response.data)}`, 'CLAIM_UPSERT')
      }
    }
  }

  async createClaimWithAnotherAccount(data: PayloadForCreateClaimWithAnotherAccount[]) {
    const { token } = (await this.userService.login({ credentials: microCalcCredentials })) as LoginResult
    this.datService.setToken(token)
    const result: CreateClaimWithAnotherAccountResponse[] = []
    for (const item of data) {
      const id = await this.myClaimService.createOrUpdateContract(item.claimData)
      const calcResult = await this.myClaimService.calculateContractN({
        contractID: id,
        country: item.claimData.Dossier?.Country,
        language: item.claimData.Dossier?.Language
      })
      const assignedTag = item.tags.find(tag => tag.id === item.tagId)
      result.push({ claimId: id, assignedTag, calcResult })
    }
    return result
  }

  excludeHiddenFieldsInContract(obj: DAT.ContractFromGetContract | undefined, paths: string[] = []) {
    const result = {} as DAT.ContractFromGetContract

    paths.forEach(path => {
      const keys = path.split('.')
      let current = obj
      let tempResult = result

      if (path.includes('customTemplateData.entry')) {
        const templateDataValueAsKey = keys[keys.length - 1]
        const templateDataResult =
          (Array.isArray(obj?.customTemplateData?.entry) &&
            obj?.customTemplateData?.entry?.filter(entry => entry.key === templateDataValueAsKey)) ||
          []

        if (templateDataResult !== undefined) {
          tempResult.customTemplateData = {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            entry: [...(tempResult.customTemplateData?.entry || []), ...templateDataResult]
          }
        }
      }

      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        if (current && typeof current === 'object' && key in current) {
          if (i === keys.length - 1) {
            tempResult[key] = current[key]
          } else {
            tempResult[key] = tempResult[key] || (Array.isArray(current[key]) ? [] : {})
            tempResult = tempResult[key]
            current = current[key]
          }
        } else {
          return
        }
      }
    })

    return result
  }

  importIni(file: Express.Multer.File, headers: HttpHeaders): ImportIniResult {
    const fileContent = file.buffer.toString('utf-8')
    const mappingConfig = MOCK_MAPPING_CONFIG

    const parsedIni = parseIniFile(fileContent)
    const detectedConfig = detectIniConfig(parsedIni, mappingConfig)

    return {
      parsedIni,
      detectedConfig
    }
  }
}
