import { findSectionIgnoreCase } from './findSectionIgnoreCase'
import { parseIniFile } from './parseIniFile'
import { MOCK_INI_DATA_FOR_ALLIANZ } from '../constants'

describe('findSectionIgnoreCase', () => {
  it('should find section with exact case match', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_ALLIANZ)

    expect(findSectionIgnoreCase(parsedIni, 'MESSAGGIO')?.[0]?.['Compagnia']).toBe('352~ALLIANZ VIVA')
    expect(findSectionIgnoreCase(parsedIni, 'MEssAGgIO')?.[0]?.['Compagnia']).toBe('352~ALLIANZ VIVA')
    expect(findSectionIgnoreCase(parsedIni, '')).toBeNull()
    expect(findSectionIgnoreCase(parsedIni, undefined)).toBeNull()
  })
})
