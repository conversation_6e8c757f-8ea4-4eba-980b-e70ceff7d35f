import * as dotenv from 'dotenv'

dotenv.config()

export const postPaymentAttachmentName = 'post-payment.hjson'

export const postPaymentAttachmentFolderId = Number(process.env.POST_PAYMENT_DOCUMENT_ID || '')

export const microCalcCredentials = { customerNumber: '3131411', user: 'wedatwebservice', password: 'weDAT2021!!$' }

export {
  MOCK_INI_DATA_FOR_AXA,
  MOCK_INI_DATA_FOR_ALLIANZ,
  MOCK_INI_DATA_FOR_SOGESA,
  MOCK_MAPPING_CONFIG
} from './importIni'
